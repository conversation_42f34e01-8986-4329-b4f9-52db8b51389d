# scraper.py (updated for new website structure)

import time
import logging
import os
import requests
import shutil
import re
import unicodedata
from urllib.parse import urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import (
    NoSuchElementException,
    TimeoutException,
    WebDriverException,
    InvalidSelectorException,
    StaleElementReferenceException
)
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
from config import Config
from app.database.db_manager import DatabaseManager
from app.database.models import Show, Episode
from app.exceptions import PageLoadException, ScraperException
from abc import ABC, abstractmethod
from app.scraping.decode import decode_base64_to_iframe  # <-- Import the decoding utility

class BaseScraper(ABC):
    def __init__(self):
        self._driver = self._init_driver()
        self.db_manager = DatabaseManager()

    def _init_driver(self):
        firefox_options = FirefoxOptions()
        if Config.FIREFOX_HEADLESS:
            firefox_options.add_argument("--headless")
        firefox_options.headless = Config.FIREFOX_HEADLESS
        firefox_options.set_preference("webdriver.accept.untrusted.certs", Config.FIREFOX_ACCEPT_UNTRUSTED_CERTS)
        firefox_options.set_preference("webdriver.assume_untrusted_issuer", Config.FIREFOX_ASSUME_UNTRUSTED_ISSUER)
        firefox_options.set_preference("dom.webnotifications.enabled", not Config.FIREFOX_DISABLE_NOTIFICATIONS)
        firefox_options.set_preference("dom.push.enabled", not Config.FIREFOX_DISABLE_PUSH)

        # Additional preferences to handle consent forms and popups
        if Config.FIREFOX_DISABLE_COOKIES_BANNER:
            firefox_options.set_preference("privacy.trackingprotection.enabled", True)
            firefox_options.set_preference("privacy.trackingprotection.socialtracking.enabled", True)
            firefox_options.set_preference("privacy.trackingprotection.cryptomining.enabled", True)
            firefox_options.set_preference("privacy.trackingprotection.fingerprinting.enabled", True)

        if Config.FIREFOX_DISABLE_GDPR_POPUP:
            # Disable various popup and consent mechanisms
            firefox_options.set_preference("dom.disable_beforeunload", True)
            firefox_options.set_preference("dom.popup_maximum", 0)
            firefox_options.set_preference("privacy.clearOnShutdown.cookies", False)
            firefox_options.set_preference("privacy.clearOnShutdown.sessions", False)

        try:
            service = FirefoxService(executable_path=Config.GECKODRIVER_PATH)
            driver = webdriver.Firefox(service=service, options=firefox_options)
            logging.info("Firefox WebDriver initialized successfully.")
            return driver
        except Exception as e:
            logging.error(f"Failed to initialize Firefox WebDriver: {e}")
            raise

    @abstractmethod
    def scrape(self):
        pass

    def close(self):
        if self._driver:
            self._driver.quit()
            logging.info("WebDriver quit.")
        self.db_manager.close()
        logging.info("Database connection closed.")

class DonghuaScraper(BaseScraper):
    def __init__(self):
        super().__init__()
        self.base_url = Config.BASE_URL

    def scrape(self):
        logging.info("Starting scraping process.")
        if not Config.scraper_lock.acquire(blocking=False):
            logging.info("Scraper is already running. New run request ignored.")
            return "Scraper is already running. New run request ignored."

        summary = ""
        try:
            episode_list = self._scrape_episodes()
            logging.info(f"Scraper found {len(episode_list)} episodes to process.")
            new_shows_count = len(episode_list) if episode_list else 0
            current_date = time.strftime('%Y-%m-%d')
            summary = f"Returned {new_shows_count} new shows on {current_date}"
            print(summary)
            logging.info(summary)

            if episode_list:
                self._insert_episodes(episode_list)
                logging.info("Episodes processing completed.")
        except ScraperException as e:
            logging.error(f"Scraper error: {e}")
            summary = f"Scraper error: {e}"
        except Exception as e:
            logging.exception("An unexpected error occurred during scraping.")
            summary = f"Unexpected error during scraping: {e}"
        finally:
            self.close()
            Config.scraper_lock.release()
            logging.info("Scraper lock released.")
            return summary

    def _scrape_episodes(self):
        """
        Scrape episode URLs from the main page, then process each episode individually.
        """
        logging.info(f"Accessing {self.base_url}")
        try:
            self._driver.get(self.base_url)
            WebDriverWait(self._driver, Config.ARTICLE_LOAD_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, 'body'))
            )

            # Handle any consent forms or popups that might appear
            self._handle_consent_forms()

        except WebDriverException as e:
            logging.error(f"{Config.ERROR_PAGE_LOAD}: {e}")
            raise PageLoadException(Config.ERROR_PAGE_LOAD)

        # Scroll down to load more content
        self._scroll_down()

        # Find episode links from article elements
        episode_list = []
        try:
            episode_links = self._driver.find_elements(By.CSS_SELECTOR, 'article a')

            episode_set = set()
            for link_tag in episode_links:
                try:
                    episode_url = link_tag.get_attribute('href')
                    title = link_tag.get_attribute('title') or link_tag.text.strip()

                    if episode_url and ('episode' in episode_url.lower() or 'multiple-subtitles' in episode_url.lower()):
                        # Normalize the URL to use the base domain
                        episode_url = self._normalize_url(episode_url)

                        if episode_url not in episode_set:
                            episode_set.add(episode_url)
                            episode_list.append((title, episode_url))

                except Exception as e:
                    logging.debug(f"Error processing episode link: {e}")

        except Exception as e:
            logging.error(f"Error finding episode links: {e}")

        return episode_list

    def _scroll_down(self):
        """Scroll down to load more content."""
        try:
            # Wait for the body element to be present
            WebDriverWait(self._driver, Config.PAGE_LOAD_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, 'body'))
            )

            # Get initial height
            last_height = self._driver.execute_script("return document.body.scrollHeight")

            # Scroll down to load more content
            while True:
                self._driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                try:
                    WebDriverWait(self._driver, Config.SCROLL_WAIT_TIMEOUT).until(
                        lambda d: d.execute_script("return document.body.scrollHeight") > last_height
                    )
                    WebDriverWait(self._driver, Config.SCROLL_WAIT_TIMEOUT).until(
                        EC.presence_of_element_located((By.TAG_NAME, 'article'))
                    )
                except TimeoutException:
                    break
                except Exception as e:
                    logging.debug(f"Error while scrolling: {e}")
                    break

                new_height = self._driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                time.sleep(Config.SCROLL_PAUSE_TIME)
        except Exception as e:
            logging.debug(f"Error in _scroll_down: {e}")

    def _get_episode_details(self, url, retries=3):
        """
        Get episode details from an individual episode page.
        Handles the new website structure with "Select Server" overlay.
        """
        # Normalize the URL to use the base domain
        url = self._normalize_url(url)
        logging.debug(f"Processing episode: {url}")

        for attempt in range(retries):
            try:
                self._driver.get(url)

                WebDriverWait(self._driver, Config.PAGE_LOAD_TIMEOUT).until(
                    EC.presence_of_element_located((By.TAG_NAME, 'body'))
                )

                # Wait for page to fully load and JavaScript to execute
                time.sleep(5)

                # Handle any consent forms on the episode page
                self._handle_consent_forms()

                # Extract show title from meta tag
                try:
                    meta_tag = self._driver.find_element(By.XPATH, '//meta[@property="article:section"]')
                    show_title = meta_tag.get_attribute('content').strip()
                except Exception:
                    logging.debug(f"Meta tag not found on page {url}. Skipping.")
                    return 'no_iframe', None, None, False, None

                # Extract thumbnail URL and release date
                thumbnail_url = self._extract_thumbnail_url(url)
                release_date = self._extract_release_date(url)

                # Handle the server selection interface
                iframe_src = self._handle_server_selection()

                if not iframe_src:
                    logging.debug(f"No iframe found for: {url}")
                    return 'no_iframe', show_title, thumbnail_url, False, release_date

                return [iframe_src], show_title, thumbnail_url, False, release_date

            except (TimeoutException, WebDriverException, InvalidSelectorException, StaleElementReferenceException) as e:
                logging.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                time.sleep(5)

        logging.error(f"Failed to process {url} after {retries} attempts")
        raise PageLoadException(f"Failed to load page {url} after {retries} attempts.")

    def _handle_consent_forms(self):
        """
        Handle various consent forms, cookie banners, and GDPR popups that might interfere with scraping.
        """
        try:
            # Wait a moment for any popups to appear
            time.sleep(2)

            # Common consent form selectors to look for and dismiss
            consent_selectors = [
                # Generic consent buttons
                "button[id*='accept']",
                "button[class*='accept']",
                "button[id*='consent']",
                "button[class*='consent']",
                "button[id*='agree']",
                "button[class*='agree']",
                "button[id*='cookie']",
                "button[class*='cookie']",
                "button[id*='gdpr']",
                "button[class*='gdpr']",
                # Close buttons for overlays
                ".close",
                ".modal-close",
                "[aria-label='Close']",
                "[data-dismiss='modal']",
            ]

            for selector in consent_selectors:
                try:
                    elements = self._driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        for element in elements:
                            try:
                                if element.is_displayed():
                                    element.click()
                                    logging.info(f"Dismissed consent form using selector: {selector}")
                                    time.sleep(1)
                                    break
                            except Exception:
                                continue
                except Exception:
                    continue

            # Try text-based consent button detection
            consent_texts = ['Accept', 'Agree', 'OK', 'Continue', 'I Agree', 'Accept All']
            for text in consent_texts:
                try:
                    script = f"""
                    var buttons = document.querySelectorAll('button, a, div[role="button"]');
                    for (var i = 0; i < buttons.length; i++) {{
                        if (buttons[i].textContent.toLowerCase().includes('{text.lower()}')) {{
                            buttons[i].click();
                            return true;
                        }}
                    }}
                    return false;
                    """
                    result = self._driver.execute_script(script)
                    if result:
                        logging.info(f"Dismissed consent form using text: {text}")
                        time.sleep(1)
                        break
                except Exception:
                    continue

            # Remove overlay elements that might block content
            overlay_removal_script = """
            var overlays = document.querySelectorAll('[style*="position: fixed"], [style*="position:fixed"], .overlay, .modal-backdrop, .consent-overlay, .cookie-banner, .gdpr-banner');
            for (var i = 0; i < overlays.length; i++) {
                var overlay = overlays[i];
                if (overlay && overlay.style && (overlay.style.zIndex > 1000 || overlay.style.position === 'fixed')) {
                    overlay.remove();
                }
            }
            """
            self._driver.execute_script(overlay_removal_script)

        except Exception as e:
            logging.debug(f"Error handling consent forms: {e}")

    def _handle_server_selection(self):
        """
        Handle the server selection interface.
        Clicks the "Select Server" overlay and finds the "All Sub Player" option.
        """
        try:
            # Look for the server selection button
            select_server_selectors = [
                ".server-select-button",
                "button.server-select-button",
                "[class*='server-select']",
                "//button[contains(text(), 'Select Server')]"
            ]

            select_server_element = None
            for selector in select_server_selectors:
                try:
                    if selector.startswith("//"):
                        select_server_element = self._driver.find_element(By.XPATH, selector)
                    else:
                        select_server_element = self._driver.find_element(By.CSS_SELECTOR, selector)

                    if select_server_element:
                        break
                except NoSuchElementException:
                    continue

            if not select_server_element:
                logging.debug("Could not find 'Select Server' element")
                return None

            # Click the select server element
            self._driver.execute_script("arguments[0].click();", select_server_element)
            time.sleep(3)  # Wait for server options to load

            # Wait for server selection interface to appear
            try:
                WebDriverWait(self._driver, 10).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".server-grid-item, button.server-grid-item")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".server-selection-panel")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='server'][data-value]"))
                    )
                )
            except TimeoutException:
                logging.debug("Server selection interface did not load")
                return None

            # Find the "All Sub Player" server option
            return self._find_all_sub_player_source()

        except Exception as e:
            logging.debug(f"Error in server selection: {e}")
            return None

    def _find_all_sub_player_source(self):
        """
        Find and extract the iframe source from the "All Sub Player" server option.
        """
        try:
            # Look for server elements with various possible selectors
            server_selectors = [
                "button.server-grid-item",
                ".server-grid-item",
                "[class*='server'][data-value]",
                "button[data-value]",
                ".server-selection-panel button",
                ".server-selection-panel [data-value]"
            ]

            server_buttons = []
            for selector in server_selectors:
                try:
                    elements = self._driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        server_buttons.extend(elements)
                except Exception as e:
                    logging.debug(f"Error with selector {selector}: {e}")

            # Remove duplicates while preserving order
            seen = set()
            unique_buttons = []
            for button in server_buttons:
                button_id = id(button)
                if button_id not in seen:
                    seen.add(button_id)
                    unique_buttons.append(button)

            server_buttons = unique_buttons

            if not server_buttons:
                logging.debug("No server elements found")
                return None

            # Look for "All Sub Player" option
            for button in server_buttons:
                try:
                    button_text = button.text.strip().lower()

                    # Check if this is the "All Sub Player" option
                    if "all sub" in button_text or "all sub player" in button_text:
                        # Get the data-value attribute
                        encoded_data = button.get_attribute("data-value")
                        if encoded_data:
                            # Decode the base64 data
                            try:
                                iframe_html = decode_base64_to_iframe(encoded_data)

                                # Parse the iframe HTML to extract the src
                                soup = BeautifulSoup(iframe_html, "html.parser")
                                iframe = soup.find("iframe")

                                if iframe and iframe.get("src"):
                                    iframe_src = iframe["src"]
                                    return iframe_src
                                else:
                                    logging.debug("No iframe or src found in decoded HTML")
                            except Exception as e:
                                logging.debug(f"Error decoding data-value: {e}")
                        else:
                            logging.debug("No data-value attribute found on All Sub Player button")
                except Exception as e:
                    logging.debug(f"Error processing server button: {e}")

            # If "All Sub Player" not found, try any button with data-value
            for button in server_buttons:
                try:
                    encoded_data = button.get_attribute("data-value")
                    if encoded_data:
                        try:
                            iframe_html = decode_base64_to_iframe(encoded_data)
                            soup = BeautifulSoup(iframe_html, "html.parser")
                            iframe = soup.find("iframe")

                            if iframe and iframe.get("src"):
                                iframe_src = iframe["src"]
                                return iframe_src
                        except Exception as e:
                            logging.debug(f"Error decoding server option: {e}")
                except Exception as e:
                    logging.debug(f"Error processing server button: {e}")

            logging.debug("No valid server options found")
            return None

        except Exception as e:
            logging.error(f"Error finding server source: {e}")
            return None

    def _insert_episodes(self, episode_list):
        """
        Process each episode URL to extract details and insert into database.
        """
        for episode_title, episode_page_url in episode_list:
            try:
                existing_episode = Episode.get_by_episode_page_url(self.db_manager, episode_page_url)
                if existing_episode:
                    if existing_episode.is_preview == 0:
                        continue
                    else:
                        # Update existing preview episode
                        iframe_srcs, show_title, thumbnail_url, is_preview, release_date = self._get_episode_details(episode_page_url)
                        if iframe_srcs == 'no_iframe':
                            continue
                        if not iframe_srcs:
                            logging.debug(f"No iframe src found for episode: {episode_title}")
                            continue

                        # Update the existing episode
                        existing_episode.iframe_src = iframe_srcs[0]
                        existing_episode.watched = 0
                        existing_episode.is_preview = 0
                        existing_episode.save(self.db_manager)
                        logging.info(f"Updated episode: {episode_title}")
                else:
                    # Process new episode
                    iframe_srcs, show_title, thumbnail_url, is_preview, release_date = self._get_episode_details(episode_page_url)
                    if iframe_srcs == 'no_iframe':
                        continue
                    if not iframe_srcs:
                        logging.debug(f"No iframe src found for episode: {episode_title}")
                        continue

                    # Get or create show
                    show = Show.get_by_title(self.db_manager, show_title)
                    if not show:
                        # Create new show with thumbnail URL
                        show = Show(id=None, title=show_title, url=None)
                        show.save(self.db_manager)

                        # If we found a thumbnail, download and save it
                        if thumbnail_url:
                            self._download_and_save_thumbnail(thumbnail_url, show.id, show_title)
                    elif not show.thumbnail_path and thumbnail_url:
                        # If show exists but has no thumbnail, download and save it
                        self._download_and_save_thumbnail(thumbnail_url, show.id, show_title)

                    # Create the episode object
                    episode = Episode(
                        id=None,
                        title=episode_title,
                        episode_page_url=episode_page_url,
                        iframe_src=iframe_srcs[0],
                        show_id=show.id,
                        watched=0,
                        is_preview=1 if is_preview else 0,
                        created_at=release_date or time.strftime('%Y-%m-%d %H:%M:%S')
                    )

                    episode.save(self.db_manager)
                    logging.info(f"Added new episode: {episode_title}")

                time.sleep(Config.SCREENSHOT_DELAY)
            except ScraperException as e:
                logging.error(f"Error processing episode '{episode_title}': {e}")
                continue
            except Exception as e:
                logging.error(f"Unexpected error processing episode '{episode_title}': {e}")
                continue

    def _normalize_url(self, url):
        """Normalize URL to use the base domain."""
        if not url.startswith('http'):
            return f"https://donghuastream.org/{url.lstrip('/')}"
        return url

    def _extract_thumbnail_url(self, url):
        """Extract thumbnail URL from the episode page."""
        thumbnail_url = None
        try:
            # Look for the thumbnail image using various possible selectors
            selectors = [
                'img.wp-post-thumbnail',
                'img.attachment-post-thumbnail',
                'img[class*="post-thumbnail"]',
                'img[data-src*="wp-content/uploads"]'
            ]

            for selector in selectors:
                try:
                    thumbnail_img = self._driver.find_element(By.CSS_SELECTOR, selector)
                    if thumbnail_img:
                        # Try data-src first as it might contain the full-resolution image
                        thumbnail_url = thumbnail_img.get_attribute('data-src') or thumbnail_img.get_attribute('src')
                        if thumbnail_url:
                            break
                except NoSuchElementException:
                    continue

            if not thumbnail_url:
                logging.debug(f"No thumbnail found on page {url}")
        except Exception as e:
            logging.debug(f"Error finding thumbnail on page {url}: {e}")

        return thumbnail_url

    def _extract_release_date(self, url):
        """Extract release date from the episode page."""
        release_date = None
        try:
            # Look for the release date in the span with class="updated"
            updated_span = self._driver.find_element(By.XPATH, "//span[@class='updated']")
            if updated_span:
                date_str = updated_span.text.strip()

                # Parse the date
                from datetime import datetime
                try:
                    # Try to parse with format "April 13, 2025"
                    release_date = datetime.strptime(date_str, '%B %d, %Y').strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    try:
                        # Try alternative format without comma "April 13 2025"
                        release_date = datetime.strptime(date_str, '%B %d %Y').strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        logging.debug(f"Could not parse date format: {date_str}")
                        release_date = None
        except Exception as e:
            logging.debug(f"Could not extract release date from page {url}: {e}")
            release_date = None

        return release_date

    def _download_and_save_thumbnail(self, thumbnail_url, show_id, show_title):
        """Download and save a thumbnail image from the episode page."""
        try:
            # Create thumbnails directory if it doesn't exist
            thumbnails_dir = os.path.join(Config.BASE_DIR, 'app', 'web', 'static', 'thumbnails')
            if not os.path.exists(thumbnails_dir):
                os.makedirs(thumbnails_dir)

            # Generate a filename based on the show title - remove non-ASCII characters
            # First, normalize the title to remove any special characters
            normalized_title = unicodedata.normalize('NFKD', show_title)
            # Then, remove any non-ASCII characters and replace spaces/special chars with underscores
            safe_title = re.sub(r'[^\x00-\x7F]', '', normalized_title)
            safe_title = re.sub(r'[^\w\-_]', '_', safe_title).strip('_')

            # If safe_title is empty after cleaning, use a generic name
            if not safe_title:
                safe_title = f"show_{show_id}"

            parsed_url = urlparse(thumbnail_url)
            original_filename = os.path.basename(parsed_url.path)
            extension = os.path.splitext(original_filename)[1] or '.jpg'
            filename = f"{safe_title}_{show_id}{extension}"
            filepath = os.path.join(thumbnails_dir, filename)

            # Download the image
            response = requests.get(thumbnail_url, stream=True)

            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    response.raw.decode_content = True
                    shutil.copyfileobj(response.raw, f)

                # Generate the relative path for the database
                relative_path = f"/static/thumbnails/{filename}"

                # Update the show record with the thumbnail path
                self.db_manager.update_show_thumbnail(show_id, relative_path)

                return relative_path
            else:
                logging.debug(f"Failed to download image from {thumbnail_url}: {response.status_code}")
                return None
        except Exception as e:
            logging.debug(f"Error downloading thumbnail: {e}")
            return None
