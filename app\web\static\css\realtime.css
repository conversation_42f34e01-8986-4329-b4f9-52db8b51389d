/* app/web/static/css/realtime.css */
/* Real-time WebSocket UI Components */

/* WebSocket Connection Status Indicator */
.ws-status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    z-index: 1000;
    transition: all 0.3s ease;
    cursor: help;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ws-status-indicator.ws-connected {
    background-color: #28a745;
    color: #28a745;
    animation: pulse-green 2s infinite;
}

.ws-status-indicator.ws-disconnected {
    background-color: #ffc107;
    color: #ffc107;
    animation: pulse-yellow 1s infinite;
}

.ws-status-indicator.ws-error {
    background-color: #dc3545;
    color: #dc3545;
    animation: pulse-red 0.5s infinite;
}

.ws-status-indicator.ws-failed {
    background-color: #6c757d;
    color: #6c757d;
}

/* Pulse animations for status indicator */
@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes pulse-yellow {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

@keyframes pulse-red {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* Scrape Update Widget */
.scrape-update-widget {
    background-color: rgba(45, 45, 58, 0.4);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 3px solid #4a90e2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.scrape-update-widget:hover {
    background-color: rgba(50, 50, 65, 0.5);
    transform: translateX(2px);
}

.scrape-update-content {
    color: #e0e0e0;
}

.scrape-update-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.scrape-update-icon {
    font-size: 1.1rem;
    margin-right: 8px;
}

.scrape-update-title {
    font-weight: 600;
    font-size: 0.9rem;
    color: #ffffff;
}

.scrape-update-stats {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.stat-value {
    color: #ffffff;
    font-weight: 600;
}

.stat-success {
    color: #28a745 !important;
}

.stat-warning {
    color: #ffc107 !important;
}

.stat-error {
    color: #dc3545 !important;
}

/* Flash animation for new updates */
.scrape-update-flash {
    animation: flash-update 2s ease-in-out;
}

@keyframes flash-update {
    0% {
        background-color: rgba(45, 45, 58, 0.4);
        border-left-color: #4a90e2;
    }
    25% {
        background-color: rgba(40, 167, 69, 0.2);
        border-left-color: #28a745;
    }
    50% {
        background-color: rgba(40, 167, 69, 0.3);
        border-left-color: #28a745;
    }
    75% {
        background-color: rgba(40, 167, 69, 0.2);
        border-left-color: #28a745;
    }
    100% {
        background-color: rgba(45, 45, 58, 0.4);
        border-left-color: #4a90e2;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ws-status-indicator {
        top: 10px;
        right: 10px;
        width: 10px;
        height: 10px;
        font-size: 10px;
        line-height: 10px;
    }
    
    .scrape-update-widget {
        padding: 12px;
        margin: 10px 0;
    }
    
    .scrape-update-title {
        font-size: 0.85rem;
    }
    
    .stat-item {
        font-size: 0.75rem;
    }
}

/* Integration with existing sidebar styles */
.sidebar .scrape-update-widget {
    margin-left: 0;
    margin-right: 0;
}

/* Loading state for widget */
.scrape-update-loading {
    opacity: 0.6;
    pointer-events: none;
}

.scrape-update-loading .scrape-update-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Notification-style updates */
.scrape-notification {
    position: fixed;
    top: 50px;
    right: 20px;
    background-color: rgba(40, 167, 69, 0.95);
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    font-size: 0.9rem;
}

.scrape-notification.show {
    transform: translateX(0);
}

.scrape-notification .notification-close {
    float: right;
    margin-left: 10px;
    cursor: pointer;
    font-weight: bold;
    opacity: 0.7;
}

.scrape-notification .notification-close:hover {
    opacity: 1;
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
    .scrape-update-widget {
        background-color: rgba(30, 30, 40, 0.4);
        border-left-color: #5a9fd4;
    }

    .scrape-update-widget:hover {
        background-color: rgba(35, 35, 50, 0.5);
    }
}
