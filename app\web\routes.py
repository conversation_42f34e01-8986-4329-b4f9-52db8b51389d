# app/web/routes.py
from fastapi import APIRouter, Request, Form, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from app.scraping.scraper import Donghua<PERSON>craper
from app.database.db_manager import DatabaseManager
from app.web.websocket_manager import websocket_manager
import threading
import os
import logging
import time
import json
from datetime import datetime, timedelta

router = APIRouter()

templates = Jinja2Templates(directory=os.path.join("app", "web", "templates"))

# Dependency to provide a DatabaseManager instance
def get_db():
    """
    Create a new database connection for each request to avoid threading issues.
    """
    db = DatabaseManager()
    try:
        yield db
    finally:
        db.close()

# Main route to load the homepage with shows grid
@router.get("/", response_class=HTMLResponse)
def read_root(request: Request, db: DatabaseManager = Depends(get_db)):
    try:
        # Fetch all shows that are being watched and have unwatched episodes
        shows = db.fetch_all_shows(only_with_unwatched=True)

        # Check if there are no unwatched episodes overall
        no_unwatched_episodes = not db.are_there_any_unwatched_episodes()

        return templates.TemplateResponse("index.html", {
            "request": request,
            "shows": shows,
            "no_unwatched_episodes": no_unwatched_episodes,
        })
    except Exception as e:
        logging.error(f"Error in read_root: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to view episodes for a specific show
@router.get("/show/{show_id}", response_class=HTMLResponse)
def view_show(request: Request, show_id: int, db: DatabaseManager = Depends(get_db)):
    try:
        # Fetch the show details
        db.cursor.execute("SELECT id, title, thumbnail_path FROM shows WHERE id = ?", (show_id,))
        show = db.cursor.fetchone()

        if not show:
            raise HTTPException(status_code=404, detail="Show not found")

        # Fetch the episodes for the selected show
        episodes = db.fetch_unwatched_episodes(show_id)

        return templates.TemplateResponse("show_detail.html", {
            "request": request,
            "show": show,
            "episodes": episodes,
            "show_has_no_episodes": len(episodes) == 0
        })
    except Exception as e:
        logging.error(f"Error in view_show: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to mark an episode as watched
@router.post("/mark-watched")
async def mark_watched(episode_id: int = Form(...), show_id: int = Form(...), db: DatabaseManager = Depends(get_db)):
    try:
        # Get episode info before marking as watched
        db.cursor.execute("SELECT title FROM episodes WHERE id = ?", (episode_id,))
        episode_result = db.cursor.fetchone()
        episode_title = episode_result[0] if episode_result else "Unknown Episode"

        # Mark the episode as watched
        db.mark_episode_as_watched(episode_id)

        # Broadcast episode update
        await websocket_manager.broadcast_episode_update({
            "episode_id": episode_id,
            "show_id": show_id,
            "episode_title": episode_title,
            "action": "marked_watched"
        })

        # Check if there are any remaining unwatched episodes for this show
        has_remaining = db.has_remaining_unwatched_episodes(show_id)

        if has_remaining:
            # If there are more episodes, stay on the show page
            return RedirectResponse(url=f"/show/{show_id}", status_code=303)
        else:
            # If no more episodes, redirect to calendar view
            return RedirectResponse(url="/calendar", status_code=303)
    except Exception as e:
        logging.error(f"Error in mark_watched: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to mark an episode as a preview
@router.post("/mark-preview")
async def mark_preview(episode_id: int = Form(...), show_id: int = Form(...), db: DatabaseManager = Depends(get_db)):
    try:
        # Get episode info before marking as preview
        db.cursor.execute("SELECT title FROM episodes WHERE id = ?", (episode_id,))
        episode_result = db.cursor.fetchone()
        episode_title = episode_result[0] if episode_result else "Unknown Episode"

        # Mark the episode as preview
        db.mark_episode_as_preview(episode_id)

        # Broadcast episode update
        await websocket_manager.broadcast_episode_update({
            "episode_id": episode_id,
            "show_id": show_id,
            "episode_title": episode_title,
            "action": "marked_preview"
        })

        # Check if there are any remaining unwatched episodes for this show
        has_remaining = db.has_remaining_unwatched_episodes(show_id)

        if has_remaining:
            # If there are more episodes, stay on the show page
            return RedirectResponse(url=f"/show/{show_id}", status_code=303)
        else:
            # If no more episodes, redirect to calendar view
            return RedirectResponse(url="/calendar", status_code=303)
    except Exception as e:
        logging.error(f"Error in mark_preview: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to unmark an episode as a preview
@router.post("/unmark-preview")
async def unmark_preview(episode_id: int = Form(...), show_id: int = Form(...), db: DatabaseManager = Depends(get_db)):
    try:
        # Get episode info before unmarking as preview
        db.cursor.execute("SELECT title FROM episodes WHERE id = ?", (episode_id,))
        episode_result = db.cursor.fetchone()
        episode_title = episode_result[0] if episode_result else "Unknown Episode"

        # Unmark the episode as preview
        db.unmark_episode_as_preview(episode_id)

        # Broadcast episode update
        await websocket_manager.broadcast_episode_update({
            "episode_id": episode_id,
            "show_id": show_id,
            "episode_title": episode_title,
            "action": "unmarked_preview"
        })

        # Check if there are any remaining unwatched episodes for this show
        has_remaining = db.has_remaining_unwatched_episodes(show_id)

        if has_remaining:
            # If there are more episodes, stay on the show page
            return RedirectResponse(url=f"/show/{show_id}", status_code=303)
        else:
            # If no more episodes, redirect to calendar view
            return RedirectResponse(url="/calendar", status_code=303)
    except Exception as e:
        logging.error(f"Error in unmark_preview: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route for marking selected shows as watched has been removed

# Route to view all unwatched episodes across all shows - removed

# Route to view shows that are not being watched
@router.get("/not-watching", response_class=HTMLResponse)
def not_watching(request: Request, db: DatabaseManager = Depends(get_db)):
    try:
        # Fetch shows with unwatched episodes for the sidebar
        shows = db.fetch_shows_with_unwatched_episodes()

        # Fetch shows that are not being watched
        not_watching_shows = db.fetch_not_watching_shows()

        return templates.TemplateResponse("not_watching.html", {
            "request": request,
            "shows": shows,
            "not_watching_shows": not_watching_shows
        })
    except Exception as e:
        logging.error(f"Error in not_watching: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to view the calendar of episodes
@router.get("/calendar", response_class=HTMLResponse)
def calendar_view(request: Request, date: str = None, db: DatabaseManager = Depends(get_db)):
    try:
        from datetime import datetime, timedelta

        # If no date is provided, use today's date
        if not date:
            today = datetime.now()
            date = today.strftime('%Y-%m-%d')
        else:
            today = datetime.strptime(date, '%Y-%m-%d')

        # Calculate the start and end of the week
        # Find the previous Sunday (start of week)
        start_of_week = today - timedelta(days=today.weekday() + 1)
        if today.weekday() == 6:  # If today is Sunday
            start_of_week = today

        # End of week is 6 days after start (Saturday)
        end_of_week = start_of_week + timedelta(days=6)

        # Get the previous and next week for navigation
        prev_week = start_of_week - timedelta(days=7)
        next_week = start_of_week + timedelta(days=7)

        # Format dates for display
        week_range = f"{start_of_week.strftime('%b %d')} - {end_of_week.strftime('%b %d, %Y')}"
        prev_week_link = prev_week.strftime('%Y-%m-%d')
        next_week_link = next_week.strftime('%Y-%m-%d')

        # Get episodes for this week (only for shows being watched)
        start_date = start_of_week.strftime('%Y-%m-%d')
        end_date = end_of_week.strftime('%Y-%m-%d')
        # Get all episodes (both watched and unwatched)
        episodes = db.fetch_episodes_by_date_range(start_date, end_date, only_watching=True)

        # Organize episodes by date, filtering out duplicates
        episode_calendar = {}
        # Track unique episodes by show_id and title to avoid duplicates
        unique_episodes = {}

        for episode in episodes:
            episode_date = datetime.strptime(episode[2], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            show_id = episode[3]
            title = episode[1]

            # Create a unique key for this episode
            episode_key = f"{show_id}_{title}"

            if episode_date not in episode_calendar:
                episode_calendar[episode_date] = []

            # Only add if we haven't seen this episode before
            if episode_key not in unique_episodes:
                unique_episodes[episode_key] = True
                episode_calendar[episode_date].append(episode)

        # Get predictions for shows with patterns (only for shows being watched)
        predictions = {}
        shows = db.fetch_all_shows(only_watching=True)

        # Track which shows already have actual episodes in the current week
        shows_with_episodes_this_week = set()

        # First, collect all shows that have actual episodes in the current week
        for date, episodes_list in episode_calendar.items():
            for episode in episodes_list:
                show_id = episode[3]
                shows_with_episodes_this_week.add(show_id)

        # For each show that doesn't have an actual episode this week, predict one
        for show_id, title, _, _ in shows:
            # Skip if the show already has an actual episode this week
            if show_id in shows_with_episodes_this_week:
                continue

            # Get the future dates for this show (with detected interval pattern)
            future_dates = db.predict_future_episodes(show_id, num_predictions=1)

            # If we have a prediction and it falls within our week view
            # Only add predictions for dates that are today or in the future
            if future_dates and start_date <= future_dates[0][0] <= end_date:
                date, interval = future_dates[0]
                prediction_date = datetime.strptime(date, '%Y-%m-%d').date()
                today = datetime.now().date()

                # Only add predictions for today or future dates
                if prediction_date >= today:
                    if date not in predictions:
                        predictions[date] = []
                    predictions[date].append((show_id, title, interval))

        # Build calendar grid for the week
        calendar_days = []

        # Add each day of the week
        for day_offset in range(7):
            current_date = start_of_week + timedelta(days=day_offset)
            date_str = current_date.strftime('%Y-%m-%d')
            day_episodes = episode_calendar.get(date_str, [])
            day_predictions = predictions.get(date_str, [])

            calendar_days.append({
                'day': current_date.day,
                'weekday': current_date.strftime('%A'),  # Full weekday name
                'date': date_str,
                'episodes': day_episodes,
                'predictions': day_predictions,
                'is_today': date_str == datetime.now().strftime('%Y-%m-%d'),
                'is_future': current_date > datetime.now()
            })

        return templates.TemplateResponse("calendar.html", {
            "request": request,
            "week_range": week_range,
            "prev_week": prev_week_link,
            "next_week": next_week_link,
            "days": calendar_days,
            "today": datetime.now().strftime('%Y-%m-%d')
        })
    except Exception as e:
        logging.error(f"Error in calendar_view: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to mark a show as not watching
@router.post("/mark-not-watching")
def mark_not_watching(show_id: int = Form(...), db: DatabaseManager = Depends(get_db)):
    try:
        db.mark_show_not_watching(show_id)
        return RedirectResponse(url="/", status_code=303)
    except Exception as e:
        logging.error(f"Error in mark_not_watching: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to mark a show as watching
@router.post("/mark-watching")
def mark_watching(show_id: int = Form(...), db: DatabaseManager = Depends(get_db)):
    try:
        db.mark_show_watching(show_id)
        return RedirectResponse(url="/not-watching", status_code=303)
    except Exception as e:
        logging.error(f"Error in mark_watching: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

# Route to generate thumbnails for shows
@router.post("/generate-thumbnails")
def generate_thumbnails(db: DatabaseManager = Depends(get_db)):
    """
    Endpoint to generate thumbnails for shows that don't have them.
    """
    from app.utils.thumbnail_manager import ThumbnailManager

    def run_thumbnail_generation():
        # Create a new database connection for the thread
        thread_db = DatabaseManager()
        try:
            thumbnail_manager = ThumbnailManager(thread_db)

            # Get all shows without thumbnails
            thread_db.cursor.execute("SELECT id, title FROM shows WHERE thumbnail_path IS NULL OR thumbnail_path = ''")
            shows = thread_db.cursor.fetchall()

            for show_id, show_title in shows:
                thumbnail_manager.get_thumbnail_for_show(show_id, show_title)
                time.sleep(1)  # Avoid rate limiting

        except Exception as e:
            logging.error(f"Thumbnail generation failed: {e}")
        finally:
            thread_db.close()

    # Start the thumbnail generation in a separate thread
    thumbnail_thread = threading.Thread(target=run_thumbnail_generation, daemon=True)
    thumbnail_thread.start()
    return JSONResponse(content={"message": "Thumbnail generation started"}, status_code=200)

# New endpoint to trigger scraping manually
@router.post("/trigger-scrape")
async def trigger_scrape():
    """
    Endpoint to manually trigger the scraping process.
    Now runs synchronously to return the summary string from the scraper.
    Also broadcasts updates via WebSocket.
    """
    try:
        scraper = DonghuaScraper()
        summary = scraper.scrape()

        # Broadcast the scrape update to all connected WebSocket clients
        await websocket_manager.broadcast_scrape_update(summary)

        return JSONResponse(content={"message": summary}, status_code=200)
    except Exception as e:
        logging.error(f"Manual scraping failed: {e}")
        return JSONResponse(content={"message": f"Manual scraping failed: {e}"}, status_code=500)


# API endpoint to check a preview episode
@router.get("/api/check-preview/{episode_id}")
async def check_preview_episode(episode_id: int, db: DatabaseManager = Depends(get_db)):
    """
    API endpoint to check a specific preview episode.
    Returns JSON with the status of the check.
    """
    try:
        # Get the episode details
        db.cursor.execute(
            "SELECT id, title, episode_page_url, iframe_src, is_preview FROM episodes WHERE id = ?",
            (episode_id,)
        )
        episode = db.cursor.fetchone()

        if not episode:
            return JSONResponse(
                content={"status": "error", "message": f"Episode with ID {episode_id} not found."},
                status_code=404
            )

        episode_id, title, episode_page_url, current_iframe_src, is_preview = episode

        # Only proceed if this is a preview episode
        if is_preview != 1:
            return JSONResponse(
                content={"status": "not_preview", "message": f"Episode '{title}' is not marked as a preview."},
                status_code=200
            )

        # Create a scraper instance
        scraper = DonghuaScraper()

        try:
            # Get the episode details from the website
            logging.info(f"Checking episode page: {episode_page_url}")

            # Force the scraper to use the correct URL format
            normalized_url = episode_page_url
            if not normalized_url.startswith('https://donghuastream.org/'):
                normalized_url = f"https://donghuastream.org/{episode_page_url.split('/')[-1]}"
                logging.info(f"Normalized URL to: {normalized_url}")

            iframe_srcs, show_title, thumbnail_url, is_preview_detected, release_date = scraper._get_episode_details(normalized_url)

            # Log what we found
            logging.info(f"Current iframe_src: {current_iframe_src}")
            if iframe_srcs == 'no_iframe':
                logging.info("No iframe found on the page")
            elif not iframe_srcs:
                logging.info("Empty iframe_srcs list returned")
            else:
                logging.info(f"Found iframe_src: {iframe_srcs[0]}")

            # Close the scraper
            scraper.close()

            if iframe_srcs == 'no_iframe' or not iframe_srcs:
                # Still a preview - no iframe found
                logging.info(f"Episode '{title}' is still a preview - no iframe found")
                return JSONResponse(
                    content={"status": "still_preview", "message": f"Episode '{title}' is still a preview."},
                    status_code=200
                )

            # Check if the iframe source has changed
            # Select the best iframe source - prefer ones with 'player' in the URL
            selected_src = iframe_srcs[0]
            for src in iframe_srcs:
                if 'player' in src.lower():
                    selected_src = src
                    logging.info(f"Selected player iframe source: {selected_src}")
                    break

            if selected_src != current_iframe_src:
                # The iframe source has changed - update the episode
                logging.info(f"Iframe source has changed. Updating episode '{title}' with new source: {selected_src}")
                db.cursor.execute(
                    "UPDATE episodes SET iframe_src = ?, is_preview = 0 WHERE id = ?",
                    (selected_src, episode_id)
                )
                db.conn.commit()
                logging.info(f"Database updated successfully for episode ID {episode_id}")

                return JSONResponse(
                    content={
                        "status": "updated",
                        "message": f"Episode '{title}' has been updated with a new iframe source.",
                        "new_iframe_src": selected_src
                    },
                    status_code=200
                )
            else:
                # The iframe source is the same - still a preview
                logging.info(f"Iframe source has not changed for episode '{title}'. Still a preview.")
                logging.info(f"Current source: {current_iframe_src}")
                logging.info(f"Selected source: {selected_src}")
                return JSONResponse(
                    content={
                        "status": "still_preview",
                        "message": f"Episode '{title}' is still a preview.",
                        "current_src": current_iframe_src,
                        "found_src": selected_src
                    },
                    status_code=200
                )

        except Exception as e:
            logging.error(f"Error checking preview episode '{title}': {e}")
            return JSONResponse(
                content={"status": "error", "message": f"Error checking preview episode: {str(e)}"},
                status_code=500
            )

    except Exception as e:
        logging.error(f"Error in check_preview_episode: {e}")
        return JSONResponse(
            content={"status": "error", "message": f"Internal server error: {str(e)}"},
            status_code=500
        )


# WebSocket endpoint for real-time updates
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time updates.
    Handles connection, disconnection, and message routing.
    """
    try:
        # Extract client info from query parameters if available
        client_info = {
            "page": websocket.query_params.get("page", "unknown"),
            "user_agent": websocket.headers.get("user-agent", "unknown")
        }

        await websocket_manager.connect(websocket, client_info)

        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "ping":
                    await websocket_manager.send_personal_message({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }, websocket)

                elif message.get("type") == "page_change":
                    # Update client info when page changes
                    if websocket in websocket_manager.connection_info:
                        websocket_manager.connection_info[websocket]["client_info"]["page"] = message.get("page", "unknown")

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logging.warning("Received invalid JSON from WebSocket client")
            except Exception as e:
                logging.error(f"Error handling WebSocket message: {e}")
                break

    except Exception as e:
        logging.error(f"WebSocket connection error: {e}")
    finally:
        websocket_manager.disconnect(websocket)


# API endpoint to get WebSocket connection stats (for debugging)
@router.get("/api/websocket-stats")
def get_websocket_stats():
    """Get current WebSocket connection statistics."""
    return JSONResponse(content=websocket_manager.get_connection_stats())

