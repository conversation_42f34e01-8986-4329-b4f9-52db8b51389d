# app/web/websocket_manager.py
import json
import logging
from typing import List, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime


class WebSocketManager:
    """
    Manages WebSocket connections and broadcasts updates to connected clients.
    """
    
    def __init__(self):
        # Store active connections
        self.active_connections: List[WebSocket] = []
        # Store connection metadata
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, client_info: Dict[str, Any] = None):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # Store connection metadata
        self.connection_info[websocket] = {
            "connected_at": datetime.now(),
            "client_info": client_info or {},
            "page": client_info.get("page", "unknown") if client_info else "unknown"
        }
        
        logging.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
        
        # Send welcome message with connection status
        await self.send_personal_message({
            "type": "connection_status",
            "status": "connected",
            "message": "Real-time updates enabled",
            "timestamp": datetime.now().isoformat()
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.connection_info:
            del self.connection_info[websocket]
        
        logging.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logging.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected clients."""
        if not self.active_connections:
            logging.debug("No active WebSocket connections to broadcast to")
            return
        
        # Add timestamp to message
        message["timestamp"] = datetime.now().isoformat()
        
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logging.error(f"Error broadcasting to connection: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
        
        logging.info(f"Broadcasted message to {len(self.active_connections)} connections")
    
    async def broadcast_scrape_update(self, scrape_summary: str, stats: Dict[str, Any] = None):
        """
        Broadcast a scraping update with formatted statistics.
        
        Args:
            scrape_summary (str): Summary message from scraper
            stats (dict): Additional statistics about the scrape
        """
        # Parse basic stats from summary if not provided
        if not stats:
            stats = self._parse_scrape_summary(scrape_summary)
        
        message = {
            "type": "scrape_update",
            "summary": scrape_summary,
            "stats": stats,
            "action": "refresh_content"  # Signal frontend to refresh content
        }
        
        await self.broadcast(message)
    
    async def broadcast_episode_update(self, episode_data: Dict[str, Any]):
        """Broadcast when an episode is marked as watched."""
        message = {
            "type": "episode_update",
            "data": episode_data,
            "action": "update_episode_status"
        }
        
        await self.broadcast(message)
    
    def _parse_scrape_summary(self, summary: str) -> Dict[str, Any]:
        """
        Parse scrape summary to extract basic statistics.

        Args:
            summary (str): Summary string from scraper

        Returns:
            dict: Parsed statistics
        """
        stats = {
            "new_episodes": 0,
            "updated_previews": 0,
            "skipped_existing": 0,
            "date": datetime.now().strftime('%Y-%m-%d'),
            "time": datetime.now().strftime('%H:%M:%S'),
            "status": "completed"
        }

        try:
            import re

            # Parse new format: "Added 1 new episodes, updated 0 previews, skipped 28 existing on 2025-06-06"
            new_episodes_match = re.search(r'Added (\d+) new episodes?', summary)
            if new_episodes_match:
                stats["new_episodes"] = int(new_episodes_match.group(1))

            updated_previews_match = re.search(r'updated (\d+) previews?', summary)
            if updated_previews_match:
                stats["updated_previews"] = int(updated_previews_match.group(1))

            skipped_existing_match = re.search(r'skipped (\d+) existing', summary)
            if skipped_existing_match:
                stats["skipped_existing"] = int(skipped_existing_match.group(1))

            # Fallback: Extract number from old format like "Returned 5 new shows on 2024-01-15"
            if stats["new_episodes"] == 0:
                old_format_match = re.search(r'(\d+)\s+new\s+shows?', summary.lower())
                if old_format_match:
                    stats["new_episodes"] = int(old_format_match.group(1))

            # Extract date if present
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', summary)
            if date_match:
                stats["date"] = date_match.group(1)

        except Exception as e:
            logging.error(f"Error parsing scrape summary: {e}")

        return stats
    
    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.active_connections)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get detailed connection statistics."""
        pages = {}
        for websocket, info in self.connection_info.items():
            page = info.get("page", "unknown")
            pages[page] = pages.get(page, 0) + 1
        
        return {
            "total_connections": len(self.active_connections),
            "pages": pages,
            "uptime": datetime.now().isoformat()
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
