# app/database/db_manager.py
import sqlite3
import logging
import threading
from config import Config
from contextlib import contextmanager

class DatabaseManager:
    def __init__(self, db_path=Config.DATABASE_PATH):
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self._thread_id = threading.get_ident()
        self._setup_database()

    def _setup_database(self):
        # Enable thread-safe connections and check same thread
        self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
        self.cursor = self.conn.cursor()
        self.cursor.execute("PRAGMA foreign_keys = ON")
        # Enable WAL mode for better concurrent access
        self.cursor.execute("PRAGMA journal_mode = WAL")
        self._create_tables()

    def _create_tables(self):
        self.cursor.execute('''CREATE TABLE IF NOT EXISTS shows
                               (id INTEGER PRIMARY KEY, title TEXT UNIQUE, url TEXT, watching BOOLEAN DEFAULT 1, thumbnail_path TEXT)''')
        self.cursor.execute('''CREATE TABLE IF NOT EXISTS episodes
                               (id INTEGER PRIMARY KEY, title TEXT,
                                episode_page_url TEXT UNIQUE, iframe_src TEXT,
                                show_id INTEGER, watched BOOLEAN DEFAULT 0,
                                is_preview BOOLEAN DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                FOREIGN KEY (show_id) REFERENCES shows(id))''')
        self.conn.commit()

    def _ensure_connection(self):
        """Ensure we have a valid connection for the current thread."""
        current_thread_id = threading.get_ident()
        if self.conn is None or current_thread_id != self._thread_id:
            # Create a new connection for this thread
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.cursor = self.conn.cursor()
            self.cursor.execute("PRAGMA foreign_keys = ON")
            self.cursor.execute("PRAGMA journal_mode = WAL")
            self._thread_id = current_thread_id
            logging.debug(f"Created new database connection for thread {current_thread_id}")

    def close(self):
        if self.conn:
            self.conn.close()
            logging.debug("Database connection closed.")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.close()

    # Database operations
    def fetch_shows_with_unwatched_episodes(self):
        self._ensure_connection()
        self.cursor.execute("""
            SELECT DISTINCT shows.id, shows.title
            FROM shows
            JOIN episodes ON shows.id = episodes.show_id
            WHERE episodes.watched = 0 AND shows.watching = 1 AND episodes.is_preview = 0
            ORDER BY shows.title ASC
        """)
        shows = self.cursor.fetchall()
        return shows

    def fetch_not_watching_shows(self):
        self._ensure_connection()
        self.cursor.execute("""
            SELECT s.id, s.title, s.url, COUNT(CASE WHEN e.watched = 0 AND e.is_preview = 0 THEN e.id END) as unwatched_count
            FROM shows s
            LEFT JOIN episodes e ON s.id = e.show_id
            WHERE s.watching = 0
            GROUP BY s.id, s.title, s.url
            ORDER BY s.title ASC
        """)
        shows = self.cursor.fetchall()
        return shows

    def fetch_all_shows(self, only_with_unwatched=False, only_watching=True):
        self._ensure_connection()
        query = """
            SELECT s.id, s.title, s.thumbnail_path, COUNT(CASE WHEN e.watched = 0 AND e.is_preview = 0 THEN 1 END) as unwatched_count
            FROM shows s
            LEFT JOIN episodes e ON s.id = e.show_id
            WHERE 1=1
        """

        if only_watching:
            query += " AND s.watching = 1"

        if only_with_unwatched:
            query += " AND EXISTS (SELECT 1 FROM episodes WHERE show_id = s.id AND watched = 0 AND is_preview = 0)"

        query += """
            GROUP BY s.id, s.title, s.thumbnail_path
            ORDER BY s.title ASC
        """

        self.cursor.execute(query)
        shows = self.cursor.fetchall()
        return shows

    def update_show_thumbnail(self, show_id, thumbnail_path):
        self.cursor.execute("UPDATE shows SET thumbnail_path = ? WHERE id = ?", (thumbnail_path, show_id))
        self.conn.commit()

    def mark_show_not_watching(self, show_id):
        self.cursor.execute("UPDATE shows SET watching = 0 WHERE id = ?", (show_id,))
        self.conn.commit()

    def mark_show_watching(self, show_id):
        self.cursor.execute("UPDATE shows SET watching = 1 WHERE id = ?", (show_id,))
        self.conn.commit()

    def fetch_unwatched_episodes(self, show_id):
        # Use a subquery to get the oldest episode for each title
        self.cursor.execute("""
            SELECT e.id, e.title, e.iframe_src, e.is_preview, e.created_at
            FROM episodes e
            JOIN (
                SELECT title, MIN(created_at) as min_created_at
                FROM episodes
                WHERE show_id = ? AND watched = 0 AND is_preview = 0
                GROUP BY title
            ) oldest ON e.title = oldest.title AND e.created_at = oldest.min_created_at
            WHERE e.show_id = ? AND e.watched = 0 AND e.is_preview = 0
            ORDER BY e.created_at ASC
        """, (show_id, show_id))
        episodes = self.cursor.fetchall()
        return episodes

    def fetch_all_unwatched_episodes(self):
        # Use a subquery to get the oldest episode for each title
        self.cursor.execute("""
            SELECT e.id, e.title, e.iframe_src, e.is_preview, e.created_at, s.title as show_title, e.show_id, s.thumbnail_path
            FROM episodes e
            JOIN shows s ON e.show_id = s.id
            JOIN (
                SELECT show_id, title, MIN(created_at) as min_created_at
                FROM episodes
                WHERE watched = 0 AND is_preview = 0
                GROUP BY show_id, title
            ) oldest ON e.show_id = oldest.show_id AND e.title = oldest.title AND e.created_at = oldest.min_created_at
            WHERE e.watched = 0 AND s.watching = 1 AND e.is_preview = 0
            ORDER BY e.created_at ASC
        """)
        episodes = self.cursor.fetchall()
        return episodes

    def mark_episode_as_watched(self, episode_id):
        self._ensure_connection()
        self.cursor.execute("UPDATE episodes SET watched = 1 WHERE id = ?", (episode_id,))
        self.conn.commit()

    def mark_episode_as_preview(self, episode_id):
        self.cursor.execute("UPDATE episodes SET is_preview = 1 WHERE id = ?", (episode_id,))
        self.conn.commit()

    def unmark_episode_as_preview(self, episode_id):
        self.cursor.execute("UPDATE episodes SET is_preview = 0 WHERE id = ?", (episode_id,))
        self.conn.commit()

    def mark_all_episodes_watched(self, show_id):
        self.cursor.execute("UPDATE episodes SET watched = 1 WHERE show_id = ?", (show_id,))
        self.conn.commit()

    def are_there_any_unwatched_episodes(self):
        self._ensure_connection()
        self.cursor.execute("""
            SELECT COUNT(*)
            FROM episodes e
            JOIN shows s ON e.show_id = s.id
            WHERE e.watched = 0 AND s.watching = 1 AND e.is_preview = 0
        """)
        result = self.cursor.fetchone()
        return result[0] > 0

    def has_remaining_unwatched_episodes(self, show_id):
        """
        Check if a show has any remaining unwatched episodes (excluding previews).

        Args:
            show_id (int): The ID of the show to check

        Returns:
            bool: True if there are unwatched episodes, False otherwise
        """
        self._ensure_connection()
        self.cursor.execute("""
            SELECT COUNT(*)
            FROM episodes
            WHERE show_id = ? AND watched = 0 AND is_preview = 0
        """, (show_id,))
        result = self.cursor.fetchone()
        return result[0] > 0

    def fetch_episodes_by_date_range(self, start_date, end_date, only_watching=True):
        """
        Fetch episodes within a specific date range.

        Args:
            start_date (str): Start date in format 'YYYY-MM-DD'
            end_date (str): End date in format 'YYYY-MM-DD'
            only_watching (bool): If True, only include shows that are being watched

        Returns:
            List of episodes with show information
        """
        if only_watching:
            self.cursor.execute("""
                SELECT e.id, e.title, e.created_at, s.id as show_id, s.title as show_title, s.thumbnail_path, e.watched, e.is_preview
                FROM episodes e
                JOIN shows s ON e.show_id = s.id
                WHERE DATE(e.created_at) BETWEEN DATE(?) AND DATE(?)
                AND s.watching = 1
                ORDER BY DATE(e.created_at), s.title
            """, (start_date, end_date))
        else:
            self.cursor.execute("""
                SELECT e.id, e.title, e.created_at, s.id as show_id, s.title as show_title, s.thumbnail_path, e.watched, e.is_preview
                FROM episodes e
                JOIN shows s ON e.show_id = s.id
                WHERE DATE(e.created_at) BETWEEN DATE(?) AND DATE(?)
                ORDER BY DATE(e.created_at), s.title
            """, (start_date, end_date))

        episodes = self.cursor.fetchall()
        return episodes

    def predict_future_episodes(self, show_id, num_predictions=4):
        """
        Predict future episode release dates based on past patterns.
        Detects the actual release pattern (3-4 days vs 7 days) based on available data.

        Args:
            show_id (int): The show ID
            num_predictions (int): Number of future episodes to predict

        Returns:
            List of predicted dates
        """
        from datetime import datetime, timedelta
        import statistics
        from collections import Counter

        # Get the most recent episode for this show (excluding previews)
        self.cursor.execute("""
            SELECT created_at, title
            FROM episodes
            WHERE show_id = ? AND is_preview = 0
            ORDER BY created_at DESC
            LIMIT 1
        """, (show_id,))
        latest_episode = self.cursor.fetchone()

        # If there's no episode at all, return empty list
        if not latest_episode:
            return []

        # Get the most recent episode date
        last_date = datetime.strptime(latest_episode[0], '%Y-%m-%d %H:%M:%S')

        # Check if the episode is too old (more than 14 days)
        # If so, don't predict as the show might be on hiatus
        current_date = datetime.now()
        days_since_last_episode = (current_date - last_date).days
        if days_since_last_episode > 14:
            return []

        # Get more episodes to establish a pattern (excluding previews)
        self.cursor.execute("""
            SELECT created_at
            FROM episodes
            WHERE show_id = ? AND is_preview = 0
            ORDER BY created_at DESC
            LIMIT 10
        """, (show_id,))
        episodes = self.cursor.fetchall()

        # Default interval (7 days for weekly shows)
        interval = 7

        # If we have enough episodes to detect a pattern (3 or more)
        if len(episodes) >= 3:
            # Calculate intervals between episodes
            dates = [datetime.strptime(ep[0], '%Y-%m-%d %H:%M:%S') for ep in episodes]
            dates.reverse()  # Put in chronological order

            intervals = []
            for i in range(1, len(dates)):
                delta = dates[i] - dates[i-1]
                intervals.append(delta.days)

            # Try to detect the most common interval pattern
            interval_counts = Counter(intervals)
            most_common_interval = interval_counts.most_common(1)[0][0]

            # If there's a clear pattern (most common interval appears multiple times)
            if interval_counts[most_common_interval] >= 2 and most_common_interval > 0:
                interval = most_common_interval
            else:
                # Fall back to median interval (more robust than mean for outliers)
                interval = round(statistics.median(intervals))

            # Ensure interval is at least 1 day
            if interval < 1:
                interval = 1

        # Generate predictions
        predictions = []
        prediction_date = last_date

        for i in range(num_predictions):
            prediction_date = prediction_date + timedelta(days=interval)
            predictions.append((prediction_date.strftime('%Y-%m-%d'), interval))

        return predictions

    def get_or_create_show(self, show_title, show_url=None, watching=1, thumbnail_path=None):
        if not show_title:
            logging.warning("Show title is None. Skipping show creation.")
            return None
        # Try to fetch the show
        self.cursor.execute("SELECT id FROM shows WHERE title = ?", (show_title,))
        result = self.cursor.fetchone()
        if result:
            show_id = result[0]
            logging.info(f"Show already exists: {show_title} with id {show_id}")
            return show_id
        else:
            # Insert the show
            self.cursor.execute("INSERT INTO shows (title, url, watching, thumbnail_path) VALUES (?, ?, ?, ?)",
                               (show_title, show_url, watching, thumbnail_path))
            show_id = self.cursor.lastrowid
            self.conn.commit()
            logging.info(f"Inserted new show: {show_title} with id {show_id}")
            return show_id
